#!/bin/bash
# Script qui permet de lancer un déploiement en prenant en compte le code retour de l'application de déploiement + post script de déploiement

# Define color codes
YELLOW='\033[0;33m'  # Yellow (close to orange)
NC='\033[0m'         # No Color (reset)

# Target URL (modify this to your use case)
url="http://example.com"

# curl -X POST -H "Content-Type: application/json" --data "{\"env\":\"$DEPLOY_ENV\",\"project\":\"$CI_PROJECT_NAME\",\"state\":\"started\"}" $WORKFLOW_TRIGGER_URL

# On vérifie que les branches soient bien à jour
bash ${CI_PROJECT_DIR}/ci/add-remote-ssh.sh
set -xe
git fetch --depth=50 origin-deploy $CI_COMMIT_BRANCH
git fetch origin-deploy master hotfix

git merge-base --is-ancestor origin-deploy/master HEAD
set +x

# Execute the curl command, capturing the HTTP status code and response
echo "url to cal : " "${APIZI_DEPLOY_URL}/components/${CI_PROJECT_NAME}/envs/${DEPLOY_ENV}/versions/${VERSION}/deploy" -H 'Content-Type: application/json' -d "{\"url_zip\": \"${NEXUS_PATH}/${PROJECT_NAME_IN_NEXUS}/${VERSION}/${PROJECT_NAME_IN_NEXUS}-${VERSION}.zip\"}"
status_code=$(curl -s -w "%{http_code}" -o ./response.txt --basic -u ${basic_username}:${basic_password} "${APIZI_DEPLOY_URL}/components/${CI_PROJECT_NAME}/envs/${DEPLOY_ENV}/versions/${VERSION}/deploy" -H 'Content-Type: application/json' -d "{\"url_zip\": \"${NEXUS_PATH}/${PROJECT_NAME_IN_NEXUS}/${VERSION}/${PROJECT_NAME_IN_NEXUS}-${VERSION}.zip\"}")
echo "status_code: $status_code"
echo "response: $(cat ./response.txt)"

if [ "$status_code" -ge 400 ] && [ "$status_code" -ne 504 ]; then
  echo "Request failed with HTTP status: $status_code"
  # curl -X POST -H "Content-Type: application/json" --data "{\"env\":\"$DEPLOY_ENV\",\"project\":\"$CI_PROJECT_NAME\",\"state\":\"error\"}" $WORKFLOW_TRIGGER_URL
  exit 1
elif [ "$status_code" -eq 504 ]; then
  echo -e "${YELLOW}Request ended with timeout${NC}"
  # curl -X POST -H "Content-Type: application/json" --data "{\"env\":\"$DEPLOY_ENV\",\"project\":\"$CI_PROJECT_NAME\",\"state\":\"timeout\"}" $WORKFLOW_TRIGGER_URL
else
  # curl -X POST -H "Content-Type: application/json" --data "{\"env\":\"$DEPLOY_ENV\",\"project\":\"$CI_PROJECT_NAME\",\"state\":\"ended\"}" $WORKFLOW_TRIGGER_URL
  echo "Request successful. Status code: $status_code"
fi

git checkout -B state/${DEPLOY_ENV}
git push -f origin-deploy

if [ "$DEPLOY_ENV" == "prd" ] && ([ "$CI_COMMIT_BRANCH" == "dev" ] || [ "$CI_COMMIT_BRANCH" == "hotfix" ]); then
  git push origin-deploy state/${DEPLOY_ENV}:master
fi
if [ "$DEPLOY_ENV" == "prd" ] && ([ "$CI_COMMIT_BRANCH" == "dev" ]); then
  git push origin-deploy state/${DEPLOY_ENV}:hotfix
fi
