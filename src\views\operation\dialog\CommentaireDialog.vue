<template>
  <VDialog v-model="activeDialog" width="60%" height="90%" :persistent="persistent" v-bind="$attrs">
    <template #activator="props">
      <slot name="activator" v-bind="props" />
    </template>
    <VCard
      v-click-outside="{
        include: inclusions,
      }"
      class="content-layout"
    >
      <VCardTitle class="content-layout__header">
        <VRow class="align-center">
          <VCol> Commentaire </VCol>
          <VCol v-if="!persistent" class="flex-grow-0">
            <NjIconBtn icon="mdi-close" rounded="0" @click="activeDialog = false" />
          </VCol>
        </VRow>
      </VCardTitle>
      <VDivider />
      <VCardText class="content-layout__main pa-0">
        <VForm ref="formRef" class="h-100">
          <VContainer class="h-100 pa-3">
            <VRow class="h-100 flex-nowrap">
              <VCol cols="4" style="background-color: #f5f5f5">
                <VRow class="flex-column content-layout h-100">
                  <VCol class="content-layout__header">
                    <VRow class="flex-column">
                      <VCol>
                        <div class="align-center d-flex">
                          <span style="width: 40px"> À </span>
                          <span style="width: 100%">
                            <RemoteCombobox
                              v-model="recipients"
                              label="Destinataires"
                              prepend-inner-icon="mdi-magnify"
                              chips
                              :closable-chips="!(isSpecialMessage === 'askingAtypical' || !!parentId)"
                              class="scrollable"
                              style="--max-height: 256px"
                              :query-for-all="
                                (s, pageable) =>
                                  userApi.getAll(pageable, { active: true, emailSearch: s }).then((v) => ({
                                    ...v,
                                    data: {
                                      ...v.data,
                                      content: v.data.content.map((it) => it.email),
                                    },
                                  }))
                              "
                              :rules="activeDialog && mandatoryMessage ? [requiredRule, mailRule] : [mailRule]"
                              :readonly="isSpecialMessage === 'askingAtypical' || !!parentId"
                              hide-details="auto"
                              multiple
                              :variant="activeSearchBar === 'recipients' ? 'filled' : 'underlined'"
                              @update:focused="
                                (event: boolean) => {
                                  if (event) {
                                    activeSearchBar = 'recipients'
                                  }
                                }
                              "
                            />
                          </span>
                        </div>
                      </VCol>
                      <VCol>
                        <div class="align-center d-flex">
                          <span style="width: 40px"> Cc </span>
                          <span style="width: 100%">
                            <RemoteCombobox
                              v-model="copyRecipients"
                              label="En copie"
                              prepend-inner-icon="mdi-magnify"
                              chips
                              :closable-chips="!(isSpecialMessage === 'askingAtypical')"
                              class="scrollable"
                              style="--max-height: 256px"
                              :query-for-all="
                                (s, pageable) =>
                                  userApi.getAll(pageable, { active: true, emailSearch: s }).then((v) => ({
                                    ...v,
                                    data: {
                                      ...v.data,
                                      content: v.data.content.map((it) => it.email),
                                    },
                                  }))
                              "
                              :readonly="isSpecialMessage === 'askingAtypical'"
                              hide-details="auto"
                              multiple
                              :variant="activeSearchBar === 'copy' ? 'filled' : 'underlined'"
                              :rules="[mailRule]"
                              @update:focused="
                                (event: boolean) => {
                                  if (event) {
                                    activeSearchBar = 'copy'
                                  }
                                }
                              "
                            />
                          </span>
                        </div>
                      </VCol>
                      <VCol v-if="!isOperationToProcessMessage">
                        <VTabs v-model="tab">
                          <VTab>Contacts</VTab>
                          <VTab>{{ userIsSiege(userStore.currentUser) ? 'Assistant Texte' : 'Demandes' }}</VTab>
                        </VTabs>
                        <VDivider />
                      </VCol>
                    </VRow>
                  </VCol>
                  <VCol class="content-layout__main">
                    <VWindow v-model="tab" class="h-100">
                      <VWindowItem :class="tab === 0 ? 'content-layout' : ''" class="pa-3">
                        <VRow class="flex-column content-layout__main content-layout">
                          <VCol class="content-layout__main">
                            <VCheckbox
                              class="mb-2 ps-4"
                              :model-value="
                                activeSearchBar === 'recipients'
                                  ? !!recipients.find((rec) => rec === ceeEmail.email)
                                  : !!copyRecipients.find((cop) => cop === ceeEmail.email)
                              "
                              @update:model-value="updateList($event, ceeEmail, 'CEE Capte')"
                            >
                              <template #label>
                                <VRow class="flex-column ps-2" no-gutters>
                                  <VCol>
                                    {{ displayFullnameUser(ceeEmail) }}
                                  </VCol>
                                  <VCol style="font-size: 0.75rem; color: #9e9e9e">
                                    {{ ceeEmail.email }}
                                  </VCol>
                                </VRow>
                              </template>
                            </VCheckbox>
                            <VCheckbox
                              v-if="dafMode && operation?.entity.territory?.territoryDetails.operationTerritoryCfo"
                              class="mb-2 ps-4"
                              :model-value="
                                activeSearchBar === 'recipients'
                                  ? !!recipients.find(
                                      (rec) =>
                                        rec ===
                                        operation!.entity.territory!.territoryDetails.operationTerritoryCfo!.email
                                    )
                                  : !!copyRecipients.find(
                                      (cop) =>
                                        cop ===
                                        operation!.entity.territory!.territoryDetails.operationTerritoryCfo!.email
                                    )
                              "
                              @update:model-value="
                                updateList($event, operation.entity.territory.territoryDetails.operationTerritoryCfo)
                              "
                            >
                              <template #label>
                                <VRow class="flex-column ps-2" no-gutters>
                                  <VCol>
                                    {{
                                      displayFullnameUser(
                                        operation.entity.territory.territoryDetails.operationTerritoryCfo
                                      )
                                    }}
                                    (CFO)
                                  </VCol>
                                  <VCol style="font-size: 0.75rem; color: #9e9e9e">
                                    {{ operation.entity.territory.territoryDetails.operationTerritoryCfo.email }}
                                  </VCol>
                                </VRow>
                              </template>
                            </VCheckbox>
                            <NjExpansionPanel
                              title="Agence"
                              class="flex-grow-0"
                              :style="{
                                order: userHasRole(userStore.currentUser, 'SUPPORT_AGENCE_PLUS', 'AGENCE_PLUS') ? 2 : 1,
                              }"
                              @update:model-value="(isExpanded) => handlePanelExpansion('Agence', isExpanded)"
                            >
                              <VRow class="flex-column" dense>
                                <VCol
                                  v-for="recipient in commentStore.recipients.slice(0, agenceRecipientsToShow)"
                                  :key="recipient.id"
                                >
                                  <VCheckbox
                                    :model-value="
                                      activeSearchBar === 'recipients'
                                        ? !!recipients.find((rec) => rec === recipient.email)
                                        : !!copyRecipients.find((cop) => cop === recipient.email)
                                    "
                                    @update:model-value="updateList($event, recipient, 'Agence')"
                                  >
                                    <template #label>
                                      <VRow class="flex-column ps-2" no-gutters>
                                        <VCol>
                                          {{ displayFullnameUser(recipient) }}
                                        </VCol>
                                        <VCol style="font-size: 0.75rem; color: #9e9e9e">
                                          {{ recipient.email }}
                                        </VCol>
                                      </VRow>
                                    </template>
                                  </VCheckbox>
                                </VCol>
                                <VCol
                                  v-if="(commentStore.recipients.length ?? 0) > agenceRecipientsToShow"
                                  v-intersect="loadMoreAgenceRecipients"
                                  style="height: 60px"
                                  class="d-flex align-center justify-center"
                                >
                                  <VProgressCircular indeterminate />
                                </VCol>
                              </VRow>
                            </NjExpansionPanel>
                            <NjExpansionPanel
                              title="Siège"
                              class="flex-grow-0"
                              :style="{
                                order: userHasRole(userStore.currentUser, 'AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS') ? 1 : 2,
                              }"
                              @update:model-value="(isExpanded) => handlePanelExpansion('Siège', isExpanded)"
                            >
                              <VRow class="flex-column ps-4" dense>
                                <VCol
                                  v-for="userSiege in commentStore.siege.content.slice(0, agenceRecipientsToShow)"
                                  :key="userSiege.id"
                                >
                                  <VCheckbox
                                    :model-value="
                                      activeSearchBar === 'recipients'
                                        ? !!recipients.find((rec) => rec === userSiege.email)
                                        : !!copyRecipients.find((cop) => cop === userSiege.email)
                                    "
                                    @update:model-value="updateList($event, userSiege, 'Siège')"
                                  >
                                    <template #label>
                                      <VRow class="flex-column ps-2" no-gutters>
                                        <VCol>
                                          {{ displayFullnameUser(userSiege) }}
                                        </VCol>
                                        <VCol style="font-size: 0.75rem; color: #9e9e9e">
                                          {{ userSiege.email }}
                                        </VCol>
                                      </VRow>
                                    </template>
                                  </VCheckbox>
                                </VCol>
                              </VRow>
                            </NjExpansionPanel>
                            <NjExpansionPanel
                              title="Liste de contacts"
                              class="flex-grow-0"
                              style="order: 3"
                              @update:model-value="
                                (isExpanded) => handlePanelExpansion('Liste de contacts', isExpanded)
                              "
                            />
                          </VCol>
                        </VRow>
                      </VWindowItem>
                      <VWindowItem :class="tab === 1 ? 'content-layout' : ''">
                        <VRow class="flex-column content-layout__main">
                          <VCol>
                            <NjExpansionPanel v-if="userIsSiege(userStore.currentUser)" title="Messages enregistrés">
                              <MessageTemplateAllView :on-click-row="(item: MessageTemplate) => appendMessage(item)" />
                            </NjExpansionPanel>
                            <NjExpansionPanel
                              v-if="userStore.hasRole('AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'ADMIN_PLUS')"
                              title="Demandes"
                            >
                              <VRow class="flex-column" dense>
                                <VCol>
                                  <VCard @click="askAtypical">
                                    <VCardText class="pa-2 text-center" style="font-size: 1rem">
                                      <b> Valorisation atypique </b>
                                    </VCardText>
                                  </VCard>
                                </VCol>
                                <VCol
                                  v-if="
                                    operation &&
                                    (operation.stepId >= 110 || operation.stepId == 70) &&
                                    userStore.hasRole('AGENCE_PLUS', 'SUPPORT_AGENCE_PLUS', 'ADMIN', 'ADMIN_PLUS')
                                  "
                                >
                                  <VCard @click="sendVF">
                                    <VCardText class="pa-2 text-center" style="font-size: 1rem">
                                      <b> Envoyer la VF </b>
                                    </VCardText>
                                  </VCard>
                                </VCol>
                              </VRow>
                            </NjExpansionPanel>
                          </VCol>
                        </VRow>
                      </VWindowItem>
                    </VWindow>
                  </VCol>
                </VRow>
              </VCol>
              <VDivider vertical />
              <VCol cols="8" class="content-layout">
                <VRow class="flex-column content-layout">
                  <div class="content-layout__main">
                    <VCol v-if="mandatoryMessage">
                      <div v-for="l in mandatoryMessage.split('\n')" :key="l">
                        {{ l }}
                      </div>
                    </VCol>
                    <VCol>
                      <VTextarea
                        v-model="messageRequest.message"
                        lines
                        rows="1"
                        max-rows="15"
                        auto-grow
                        :rules="!mandatoryMessage && activeDialog ? [requiredRule] : []"
                        @paste="onPaste"
                      />
                    </VCol>
                    <VCol v-if="screenshots.length > 0">
                      <div class="d-flex align-center" style="gap: 16px">
                        <div
                          v-for="(s, index) in screenshots"
                          :key="index"
                          icon="mdi-delete"
                          color="primary"
                          class="p-relative"
                        >
                          <VBtn
                            size="x-small"
                            density="comfortable"
                            icon="mdi-delete"
                            style="position: absolute; top: -8px; right: -8px"
                            color="primary"
                            @click="screenshots.splice(index, 1)"
                          />
                          <img :src="s.dataUrl" style="max-width: 100px; max-height: 100px; object-fit: contain" />
                        </div>
                      </div>
                    </VCol>
                    <VCol v-if="isSpecialMessage === 'askingAtypical'">
                      <VRow>
                        <VCol>
                          <NjDisplayValue label="Valorisation classique">
                            <template #value>
                              <VTextField v-model="atypicalValuationRequest.classicValuationValue" type="number" />
                            </template>
                          </NjDisplayValue>
                        </VCol>
                        <VCol>
                          <NjDisplayValue label="Valorisation précarité">
                            <template #value>
                              <VTextField
                                v-model="atypicalValuationRequest.precariousnessValuationValue"
                                type="number"
                              />
                            </template>
                          </NjDisplayValue>
                        </VCol>
                      </VRow>
                    </VCol>
                    <VCol v-if="!noLink && isSpecialMessage !== 'finalVersion'">
                      <NjDisplayValue label="Lien Document">
                        <template #value>
                          <RemoteAutoComplete
                            v-model="messageRequest.concernedDocumentTypeIds"
                            :query-for-all="queryDocumentTypeInOperation"
                            :query-for-ones="queryOnesDocumentType"
                            item-title="name"
                            :readonly="props.mandatoryConcernedDocumentTypeIds !== undefined"
                            chips
                            multiple
                          />
                        </template>
                      </NjDisplayValue>
                    </VCol>
                    <VCol v-for="document in selectedDocuments" :key="document.id">
                      <VLink @click="downloadDocumentFile(document)"> {{ document.documentType.name }} </VLink>
                    </VCol>
                    <VCol v-if="!operation?.property && isSpecialMessage === 'finalVersion' && !isCertynergie()">
                      <VCard color="warning" variant="outlined">
                        <VCardTitle class="d-flex"
                          ><VIcon icon="mdi-alert" />Code installation inexistant <VSpacer /><VLink
                            size="small"
                            icon="mdi-magnify"
                            style="font-weight: initial; font-size: initial"
                            @click.stop="propertyDialog = true"
                          >
                            Installations
                          </VLink></VCardTitle
                        >
                        <VCardText
                          >Cette opération n'a pas de code installation. Si vous continuez, la VF ne sera pas envoyée
                          sur l'Espace Client car le code installation est indispensable.

                          <PropertyDisplayValue :model-value="propertyToChange" />
                          <i v-if="!propertyToChange">Veuillez sélectionner une installation</i>
                        </VCardText>
                        <!-- <VCardActions
                          ><NjBtn @click="updateProperty" :disabled="!propertyToChange"
                            >Sauvegarder</NjBtn
                          ></VCardActions
                        > -->
                      </VCard>
                    </VCol>
                  </div>
                  <VCol v-if="!isOperationToProcessMessage" class="content-layout__footer">
                    <VSwitch
                      v-if="userIsSiege(userStore.currentUser) && !isSpecialMessage"
                      v-model="messageRequest.accounted"
                      label="Comptabilisation"
                    />
                    <RemoteAutoComplete
                      v-if="messageRequest.accounted"
                      v-model="reasonIds"
                      label="Raison Comptabilisation"
                      :query-for-all="
                        (s, pageable) => messageAccountingReasonApi.findAll({ search: s, visible: true }, pageable)
                      "
                      :query-for-ones="queryOnesMessageAccountingReason"
                      item-title="reason"
                      chips
                      multiple
                      :rules="[requiredRule]"
                      infinite-scroll
                    />

                    <NjBtn v-if="isSpecialMessage === 'finalVersion'" @click="downloadVF">Visualiser la VF</NjBtn>
                  </VCol>
                </VRow>
              </VCol>
            </VRow>
          </VContainer>
        </VForm>
      </VCardText>
      <VDivider />
      <VCardActions class="content-layout__footer">
        <VSpacer />
        <NjBtn v-if="!persistent" variant="outlined" @click="activeDialog = false">Annuler</NjBtn>
        <NjBtn :loading="savingMessage.loading" @click="sendMessage">Envoyer</NjBtn>
      </VCardActions>
    </VCard>
  </VDialog>

  <PropertyDialog
    v-model="propertyDialog"
    :operation-entity="operation?.entity"
    :selected="propertyToChange ? [propertyToChange] : ([] as Property[])"
    @update:selected="handleUpdateSelectedProperty"
  />
</template>
<script lang="ts" setup>
import { isCertynergie } from '@/types/debug'
import { messageAccountingReasonApi } from '@/api/messageAccountingReason'
import { operationDocumentApi } from '@/api/operationDocument'
import { userApi } from '@/api/user'
import { useAdminConfigurationStore } from '@/stores/adminConfiguration'
import { useDialogStore } from '@/stores/dialog'
import { useCommentStore } from '@/stores/recipients'
import { useSnackbarStore } from '@/stores/snackbar'
import { useUserStore } from '@/stores/user'
import type { EnhancedDocument } from '@/types/document'
import type { DocumentType } from '@/types/documentType'
import {
  atypicalMessageHeader,
  makeEmptyMessageRequest,
  vfMail,
  type AtypicalValuationMessageRequest,
  type Message,
  type MessageRequest,
  type MessageTemplate,
  type MetaMessageRequest,
} from '@/types/message'
import type { Operation } from '@/types/operation'
import type { Page } from '@/types/pagination'
import type { Property } from '@/types/property'
import { emailRegExp, requiredRule } from '@/types/rule'
import { displayFullnameUser, userHasRole, userIsSiege, type User } from '@/types/user'
import PropertyDialog from '@/views/dialog/PropertyDialog.vue'
import MessageTemplateAllView from '@/views/MessageTemplateAllView.vue'
import type { AxiosPromise, AxiosResponse } from 'axios'
import { cloneDeep } from 'lodash'
import { VCheckbox, VForm, VProgressCircular } from 'vuetify/components'
import PropertyDisplayValue from '../PropertyDisplayValue.vue'
import type { OperationsGroup } from '@/types/operationsGroup'
import { trace } from '@/stores/analytics'

const activeDialog = defineModel<boolean>({
  default: false,
})
const mandatoryMessage = defineModel<string>('mandatory-message')
const recipients = defineModel<string[]>('recipients', {
  default: () => [],
})
const copyRecipients = defineModel<string[]>('copy-recipients', {
  default: () => [],
})

const props = withDefaults(
  defineProps<{
    operation?: Operation
    operationGroup?: OperationsGroup
    businessPlanId?: number
    message?: string
    mandatoryConcernedDocumentTypeIds?: number[]
    noLink?: boolean
    /**
     * @deprecated
     */
    advisedRecipient?: string[]
    /**
     * @deprecated Trouver un autre moyen de le faire
     */
    advisedCopyRecipient?: string[]
    parentId?: number
    metaMessageRequest?: MetaMessageRequest
    persistent?: boolean
    dafMode?: boolean
    init?: 'vf'
    notSend?: boolean
  }>(),
  {
    persistent: false,
    noLink: false,
    dafMode: false,
  }
)
const emit = defineEmits<{
  'update:message': [string]
  send: [type?: 'finalVersion' | 'askingAtypical']
  close: []
}>()
const snackbarStore = useSnackbarStore()
const userStore = useUserStore()

const inclusions = () => {
  return [document.querySelector('.v-menu')]
}

const formRef = ref<VForm | null>(null)
const tab = ref(0)
const agenceRecipientsToShow = ref(0)
const messageRequest = ref(makeEmptyMessageRequest())
const reasonIds = ref<number[]>([])

const atypicalValuationRequest = ref<AtypicalValuationMessageRequest>(makeEmptyAtypicalValuationMessageRequest())
const isOperationToProcessMessage = computed(
  () => props.metaMessageRequest?.['@type'] === 'OperationToProcessMessageRequest'
)

const commentStore = useCommentStore()

const activeSearchBar = ref<'copy' | 'recipients'>('recipients')

const documentsAvailable = ref(emptyValue<Page<EnhancedDocument>>())
const selectedDocuments = ref<EnhancedDocument[]>([])

const queryDocumentTypeInOperation = (search: string): AxiosPromise<Page<DocumentType>> => {
  if (props.operation?.id) {
    return documentTypeApi.getAll({}, { activeInOperation: props.operation.id, search })
  } else if (props.businessPlanId) {
    return Promise.resolve({
      statusText: '',
      config: {},
      headers: {},
      status: 200,
      data: makeEmptyPage<DocumentType>(),
    } as AxiosResponse)
  } else {
    return documentTypeApi.getAll({}, { activeInOperationGroup: props.operationGroup?.id, search })
  }
}

const queryOnesDocumentType = (ids: number[]) => {
  return documentTypeApi.getAll(
    { size: 50 },
    { ids, activeInOperation: props.operation?.id, activeInOperationGroup: props.operationGroup?.id }
  )
}

const queryOnesMessageAccountingReason = (ids: number[]) => {
  return messageAccountingReasonApi.findAll({ ids, visible: true }, { size: 50 })
}

const propertyToChange = ref<Property | null>()

watch(activeDialog, () => {
  isSpecialMessage.value = undefined
  propertyToChange.value = props.operation?.property
})

const updateList = (event: boolean | null, user: User, groupLabel?: string) => {
  console.debug('update list', event, user)
  trace('commentClickSwitchRecipient', {
    activeSearchBar: activeSearchBar.value,
    groupLabel: groupLabel,
  })
  if (isSpecialMessage.value === 'askingAtypical' || (props.parentId && activeSearchBar.value === 'recipients')) {
    return
  }
  if (user.email) {
    if (event) {
      if (activeSearchBar.value === 'recipients') {
        recipients.value = recipients.value.concat(user.email)
      } else {
        copyRecipients.value = copyRecipients.value.concat(user.email)
      }
      return
    }
    if (activeSearchBar.value === 'recipients') {
      recipients.value = recipients.value.filter((rec) => rec !== user.email)
    } else {
      copyRecipients.value = copyRecipients.value.filter((rec) => rec !== user.email)
    }
  }
}

const savingMessage = ref(emptyValue<Message>())
const sendMessage = async (): Promise<{ message: MessageRequest; files?: Blob[] } | undefined> => {
  if ((await formRef.value!.validate()).valid) {
    const newMessageRequest: MessageRequest = {
      ...messageRequest.value,
      recipients: recipients.value.length === 0 ? '' : recipients.value.map((mail) => mail.trim()).join(';') + ';',
      copy: copyRecipients.value.length === 0 ? '' : copyRecipients.value.map((mail) => mail.trim()).join(';') + ';',
      parentId: props.parentId,
      message: ((mandatoryMessage.value ?? '') + '\n' + messageRequest.value.message).trim(),
      reasonIds: reasonIds.value,
      metaMessage:
        props.metaMessageRequest ??
        (isSpecialMessage.value === 'askingAtypical'
          ? atypicalValuationRequest.value
          : isSpecialMessage.value === 'finalVersion'
            ? {
                '@type': 'SendFinalVersionMessageRequest',
              }
            : null),
      operationId: props.operation?.id,
      businessPlanId: props.businessPlanId,
    }

    if (!props.notSend) {
      await handleAxiosPromise(
        savingMessage,
        messageApi.create(
          newMessageRequest,
          screenshots.value.map((it) => it.blob)
        ),
        {
          afterSuccess: () => {
            snackbarStore.setSuccess('Le message a bien été envoyé')
            if (isSpecialMessage.value === 'askingAtypical') {
              mandatoryMessage.value = ''
              atypicalValuationRequest.value = makeEmptyAtypicalValuationMessageRequest()
            } else if (isSpecialMessage.value === 'finalVersion') {
              mandatoryMessage.value = ''
            }
            emit('send', isSpecialMessage.value)
            activeDialog.value = false
          },
          afterError: () => {
            snackbarStore.setError(savingMessage.value.error ?? "Une erreur est survenue lors de l'envoi du message")
          },
        }
      )
    }

    return {
      message: newMessageRequest,
      files: screenshots.value.map((it) => it.blob),
    }
  } else {
    tab.value = 0
  }
}

const downloadDocumentFile = (item: EnhancedDocument) => {
  if (props.operation?.id) {
    operationDocumentApi
      .download(item.id)
      .then((response) => {
        downloadFile(item.document.originalFilename, response.data)
        snackbarStore.setSuccess('Le téléchargement a réussi')
      })
      .catch(() =>
        snackbarStore.setError(
          'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
        )
      )
    return
  }
  operationsGroupDocumentApi
    .download(item.id)
    .then((response) => {
      downloadFile(item.document.originalFilename, response.data)
      snackbarStore.setSuccess('Le téléchargement a réussi')
    })
    .catch(() =>
      snackbarStore.setError(
        'Le téléchargement du fichier a échoué. Aviez-vous envoyé ce fichier sur boCEE ? Si oui, veuillez nous contacter.'
      )
    )
}

const adminPlusUsers = ref(emptyValue<Page<User>>())

const askAtypical = async () => {
  copyRecipients.value = []
  messageRequest.value.message = ''
  if (!(isSpecialMessage.value === 'askingAtypical')) {
    const entity = props.operationGroup?.entity ?? props.operation?.entity
    isSpecialMessage.value = 'askingAtypical'
    recipients.value = [entity?.effectiveTerritoryReferent?.email ?? '']
      .concat(entity?.territory?.territoryDetails.missionManagers?.map((u) => u.email ?? '') ?? [])
      .filter((it) => it)
    messageRequest.value.accounted = false
    mandatoryMessage.value = atypicalMessageHeader
    if (!adminPlusUsers.value.value) {
      await handleAxiosPromise(
        adminPlusUsers,
        userApi.getAll({}, { roles: ['ADMIN_PLUS'], excludedRoles: ['NO_VALUE_ATYPICAL'], active: true })
      )
    }
    adminPlusUsers.value.value!.content.forEach((user) => {
      if (user.email) {
        copyRecipients.value = copyRecipients.value.concat(user.email)
      }
    })
    return
  }
  isSpecialMessage.value = undefined
  recipients.value = []
  mandatoryMessage.value = ''
}

watch(activeDialog, async (v) => {
  if (v) {
    agenceRecipientsToShow.value = 20
    messageRequest.value.operationId = props.operation?.id
    messageRequest.value.operationsGroupId = props.operationGroup?.id
    recipients.value = []
    copyRecipients.value = []
    activeSearchBar.value = 'recipients'
    if (props.advisedRecipient) {
      props.advisedRecipient
        .filter((rec) => rec.length > 0)
        .forEach((rec) => (recipients.value = recipients.value.concat(rec)))
    }
    if (props.advisedCopyRecipient) {
      props.advisedCopyRecipient
        .filter((rec) => rec.length > 0)
        .forEach((rec) => (copyRecipients.value = copyRecipients.value.concat(rec)))
    }
    if (isSpecialMessage.value === 'askingAtypical') {
      askAtypical()
      atypicalValuationRequest.value = makeEmptyAtypicalValuationMessageRequest()
    }
    screenshots.value = []
    tab.value = 0
    commentStore.load(props.operation?.id)
    return
  }
  commentStore.clear()
  messageRequest.value = makeEmptyMessageRequest()
  reasonIds.value = []
})

const appendMessage = (item: MessageTemplate) => {
  if (messageRequest.value.message == '') {
    messageRequest.value.message = item.message
  } else {
    messageRequest.value.message = messageRequest.value.message + '\n' + item.message
  }
  messageRequest.value.accounted = item.isAccountedMessage || messageRequest.value.accounted
  if (item.isAccountedMessage && item.accountingReason) {
    if (!reasonIds.value.includes(item.accountingReason.id)) {
      reasonIds.value.push(item.accountingReason.id)
    }
  }
}

watch(
  () => props.mandatoryConcernedDocumentTypeIds,
  (v) => {
    messageRequest.value.concernedDocumentTypeIds = cloneDeep(v) ?? []
  }
)
watch(
  () => messageRequest.value.concernedDocumentTypeIds,
  async (v) => {
    if (v) {
      if (props.operation?.id) {
        await handleAxiosPromise(
          documentsAvailable,
          operationDocumentApi.findAll({ operationId: props.operation.id, active: true, documentTypeIds: v }, {})
        )
      } else {
        await handleAxiosPromise(
          documentsAvailable,
          operationsGroupDocumentApi.findAll(
            { operationsGroupId: props.operationGroup?.id, active: true, documentTypeIds: v },
            {}
          )
        )
      }
      v.forEach((id) => {
        if (selectedDocuments.value?.findIndex((doc) => doc.documentType.id === id) < 0) {
          selectedDocuments.value.push(
            documentsAvailable.value.value?.content.find((opeDoc: EnhancedDocument) => opeDoc.documentType.id === id)!
          )
        }
      })
      selectedDocuments.value.forEach((doc) => {
        if (v.findIndex((id) => doc.documentType.id === id) < 0) {
          selectedDocuments.value.splice(
            selectedDocuments.value.findIndex((doc2) => doc2 === doc),
            1
          )
        }
      })
    }
  }
)

watch(
  () => props.advisedRecipient,
  (v) => {
    console.debug('watch advisedRecipient', v)
    if (v) {
      recipients.value = v
    } else {
      recipients.value = []
    }
  }
)

const screenshots = ref<
  {
    item?: DataTransferItem
    blob: File
    dataUrl: any
  }[]
>([])
const onPaste = (pasteEvent: ClipboardEvent) => {
  const item = pasteEvent.clipboardData?.items[0]

  if (item?.type.indexOf('image') === 0) {
    if (window.FileReader) {
      const blob = item.getAsFile()
      if (blob == null) {
        snackbarStore.setError('Erreur lors du chargement de votre image')
        return
      }
      const reader = new FileReader()

      reader.onload = () => {
        if (reader.result) {
          screenshots.value.push({
            item,
            blob,
            dataUrl: reader.result,
          })
        } else {
          snackbarStore.setError('Erreur lors du chargement de votre image')
        }
      }
      reader.onerror = () => {
        snackbarStore.setError('Erreur lors du chargement de votre image')
      }
      reader.readAsDataURL(blob)
    } else {
      snackbarStore.setError('Image dans le presse papier non supportée sur ce navigateur')
    }
  }
}

const loadMoreAgenceRecipients = () => {
  agenceRecipientsToShow.value += 20
}

const isSpecialMessage = ref<'askingAtypical' | 'finalVersion'>()

const sendVF = () => {
  if (isSpecialMessage.value !== 'finalVersion') {
    isSpecialMessage.value = 'finalVersion'
    mandatoryMessage.value = ''
    recipients.value = [props.operation?.beneficiary?.email ?? ''].filter((it) => it)
    copyRecipients.value = [props.operation?.applicantUser.email ?? '']
    messageRequest.value.message = vfMail(props.operation!)
  } else {
    isSpecialMessage.value = undefined
    recipients.value = []
    copyRecipients.value = []
    messageRequest.value.message = ''
  }
}

const adminConfigurationStore = useAdminConfigurationStore()

const downloadVF = () => {
  if (!adminConfigurationStore.vfDocumentTypeId) {
    snackbarStore.setError("Contacter l'administrateur pour paramétrer le document VF")
    return
  }

  operationDocumentApi
    .findAll(
      {
        operationId: props.operation?.id,
        active: true,
        documentTypeId: parseInt(adminConfigurationStore.vfDocumentTypeId!.data),
      },
      { size: 1 }
    )
    .then((responseOperationDocuments) => {
      if (responseOperationDocuments.data.totalElements) {
        operationDocumentApi
          .download(responseOperationDocuments.data.content[0].id)
          .then((response) => {
            downloadFile(responseOperationDocuments.data.content[0].document.originalFilename, response.data)
            snackbarStore.setSuccess('Le téléchargement a réussi')
          })
          .catch(async (err) => {
            snackbarStore.setError(await handleAxiosException(err))
          })
      } else {
        snackbarStore.setError("Vous n'avez pas encore importé la VF")
      }
    })
    .catch(() => snackbarStore.setError("L'opération n'a pas de document VF"))
}

const mailRule = (mails: string[]) => {
  return mails.map((mail) => mail.trim()).filter((mail) => !mail.match(emailRegExp)).length > 0
    ? 'Une ou plusieurs adresses mails sont invalides'
    : true
}

const ceeEmail: User = {
  email: '<EMAIL>',
  active: false,
  firstName: 'CEE Capte',
  lastName: '',
  function: '',
  gid: '',
  id: 0,
  roles: [],
}

// Installation
const propertyDialog = ref(false)
const dialogStore = useDialogStore()
const handleUpdateSelectedProperty = async (selected: Property[]) => {
  const response = await dialogStore.addAlert({
    title: "Confirmation du Choix définitif de l'installation",
    message:
      "Le choix de l'installation sera définitif. En cas de changement, veuillez contacter l'administration de Capte.",
    persistent: true,
    width: 640,
  })
  if (response) {
    if (selected.length) {
      propertyToChange.value = cloneDeep(selected[0])
      updateProperty()
    } else {
      propertyToChange.value = undefined
    }
  } else {
    propertyDialog.value = true
  }
}

const updating = ref(emptyValue())
const updateProperty = () => {
  handleAxiosPromise(updating, operationApi.updateProperty(props.operation!.id, propertyToChange.value!), {
    afterSuccess() {
      snackbarStore.setSuccess('Installation mise à jour')
      props.operation!.property = propertyToChange.value!
    },
    async afterError(e) {
      snackbarStore.setError(await handleAxiosException(e))
    },
  })
}

watch(activeDialog, (v, oldV) => {
  if (v && !oldV) {
    if (props.init === 'vf') {
      sendVF()
    }
  }
})

watch(
  () => props.message,
  (v) => {
    if (v !== messageRequest.value.message) {
      messageRequest.value.message = v ?? ''
    }
  },
  {
    immediate: true,
  }
)
watch(
  () => messageRequest.value.message,
  (v) => {
    if (props.message !== v) {
      emit('update:message', v)
    }
  }
)

const handlePanelExpansion = (panelName: string, isExpanded: boolean) => {
  trace('commentClickSwitchExpansion', {
    panel: panelName,
    value: isExpanded ? 'expanded' : 'collapsed',
  })
}

defineExpose({
  sendVF,
})
</script>
