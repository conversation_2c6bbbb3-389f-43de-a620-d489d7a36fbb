<template>
  <div class="nj-select nj-field">
    <label>{{ $attrs.label }}</label>
    <VSelect v-bind="{ ...$attrs, label: undefined, rules: flatten([rules, required ? [requiredRule] : []]) }">
      <template v-for="(_, slot) of $slots as {}" #[slot]="scope">
        <slot :name="slot" v-bind="scope as any" />
      </template>
      <template v-if="required" #prepend-inner>
        <div class="nj-field__required-prepend"><VIcon icon="mdi-asterisk" size="x-small" /></div>
      </template>
      <template v-else-if="recommended" #prepend-inner>
        <div class="nj-field__recommended-prepend">✦</div>
      </template>
      <template v-else #prepend-inner>
        <slot name="prepend-inner"></slot>
      </template>
    </VSelect>
  </div>
</template>

<script setup lang="ts">
import type { ValidationRule } from '@/types/rule'
import { requiredRule } from '@/types/rule'
import { flatten } from 'lodash'

withDefaults(
  defineProps<{
    rules?: Array<ValidationRule>
    required?: boolean
    recommended?: boolean
    locked?: boolean
    readonly?: boolean
  }>(),
  {
    rules: () => [],
  }
)
</script>
