import type { ControlOrderNature, ControlOrderType } from '@/types/calcul/standardizedOperationSheet'
import type { LocalDate } from '@/types/date'
import type { Operation, OperationStatus, OperationStepHistory } from '@/types/operation'
import type { OperationDuplicateAccumulationDto } from '@/types/operationDoublonCumulDto'
import type { OperationExportResultDto } from '@/types/operationExportResultDto'
import type { OperationSummary } from '@/types/operationSummary'
import type { OshCommentaryType, UpdateOperationOshPriorityRequest } from '@/types/osh/oshFolder'
import type { Page, Pageable } from '@/types/pagination'
import type { Property } from '@/types/property'
import type { Valuation } from '@/types/valuation'
import type { AxiosInstance, AxiosPromise, AxiosResponse } from 'axios'
import axiosInstance from '.'
import type { ControlOrderAfterSalesServiceStatus } from '@/types/controlOrder'

export const cumacType = ['CLASSIC', 'PRECARIOUSNESS'] as const
export type CumacType = (typeof cumacType)[number]

export const operationDelayTypes = [
  'LESS_3_MONTHS',
  'BETWEEN_3_AND_8_MONTHS',
  'BETWEEN_8_AND_10_MONTHS',
  'BETWEEN_10_AND_12_MONTHS',
] as const
export type OperationDelayType = (typeof operationDelayTypes)[number]

export type OperationFilter = Partial<{
  key: string // Ne sers que d'identifiant au différent requetes pour les logs
  search: string
  withSimulations: boolean
  myRequests: boolean
  warning: boolean
  valuationMode: boolean //only used in the front to be saved like the other filters
  stepIds: number[]
  periodIds: number[]
  atypical: null
  valuationTypeIds: []
  operationStatuses: OperationStatus[]
  commercialStatuses: []
  controlOrderNatures: ControlOrderNature[]
  noControlOrder: boolean
  standardizedOperationSheetIds: number[]
  standardizedOperationSheetCode: string
  entityNavFullIds: string[]
  entityIds: string[]
  propertyTeamEntityNavFullIds: string[]
  territoryIds: number[]
  operationsGroupIds: number[]
  inOperationsGroup: boolean
  beneficiary: string
  beneficiaryIds: number[]
  noEmmyFolder: boolean
  emmyFolderIds: number[]
  operationsGroupId: number
  notInEmmyFolderId: number
  emmyFolderId: number // 0 => sans dossier emmy, null/undefined désactive le filtre
  emmyLotIds: number[]
  emmyValidated: boolean
  operationIds: number[]
  minimumMonthsOld: number
  propertySearch: string
  endWorkDate: LocalDate
  street: string
  city: string
  controlOrderBatchId: number
  committedAfter: LocalDate
  committedBefore: LocalDate
  controlOrderTypes: ControlOrderType[]
  availableForControlOrder: boolean
  controlOrderExportTemplateIds: number[]
  toProcess: boolean
  chronoCode: string
  endedAfter: LocalDate
  endedBefore: LocalDate
  cumacTypes: CumacType[]
  available: boolean
  eligibleForSale: boolean
  oshPriority: number
  hideWithOshPriority: boolean
  selfWorks: boolean
  businessPlanIds: number[]
  goeClassicValuationValue: number
  loeClassicValuationValue: number
  goePrecariousnessValuationValue: number
  loePrecariousnessValuationValue: number
  hasEpcBonus: boolean
  instructorIds: number[]
  headOperation: boolean
  minActualEndWorksDate: LocalDate
  maxActualEndWorksDate: LocalDate
  minEstimatedEndWorksDate: LocalDate
  maxEstimatedEndWorksDate: LocalDate
  hasActualEndWorksDate: boolean | null
  minEndWorksDate: LocalDate
  maxEndWorksDate: LocalDate
  minCommitmentDate: LocalDate
  maxCommitmentDate: LocalDate
  oshHidden: boolean
  instructionDelay: '3 mois' | 'PNCEE dépassé'
  ademeCode: string
  mustSendFinalVersion: boolean
  afterSalesServiceStatuses: ControlOrderAfterSalesServiceStatus[]
  delayTypes: ('LESS_3_MONTHS' | 'BETWEEN_3_AND_8_MONTHS' | 'BETWEEN_8_AND_10_MONTHS' | 'BETWEEN_10_AND_12_MONTHS')[]
}>

export const makeEmptyFilter = (): OperationFilter => ({
  search: '',
  myRequests: false,
  warning: false,
  valuationMode: false,
  stepIds: [] as number[],
  periodIds: [],
  atypical: null,
  valuationTypeIds: [],
  operationStatuses: [] as OperationStatus[],
  commercialStatuses: [],
  controlOrderNatures: [],
  beneficiaryIds: [],
  operationsGroupIds: [],
  standardizedOperationSheetIds: [],
  entityNavFullIds: [],
  emmyFolderIds: [],
  instructorIds: [],
  territoryIds: [],
})

export interface OperationValuationRequest {
  classicValuationValue: number
  precariousnessValuationValue: number
}

class OperationApi {
  public constructor(private axios: AxiosInstance) {}

  public findAll(filter: OperationFilter, pageable: Pageable): AxiosPromise<Page<Operation>> {
    return this.axios.get('/operations', {
      params: { ...filter, ...pageable },
    })
  }

  public findAllReducedDto(
    filter: OperationFilter,
    pageable: Pageable
  ): AxiosPromise<Page<OperationDuplicateAccumulationDto>> {
    return this.axios.get('/operations/duplicate_accumulation', {
      params: { ...filter, ...pageable },
    })
  }

  public findAllWithValuations(
    filter: OperationFilter,
    pageable: Pageable
  ): AxiosPromise<
    Page<{
      operation: Operation
      valuations: Valuation[]
    }>
  > {
    return this.axios.get('/operations', {
      params: { ...filter, ...pageable, dto: 'withValuations' },
    })
  }

  public findById(id: number): AxiosPromise<Operation> {
    return this.axios.get('/operations/' + id).then((response: AxiosResponse<Operation>) => {
      response.data.coOwnerShipSyndicate = {
        address: {
          street: '',
          postalCode: '',
          city: '',
          country: 'FR',
        },
        registrationNumber: response.data.coOwnerShipSyndicateImmatriculationNumber ?? '',
        usageName: response.data.coOwnerShipSyndicateName ?? '',
      }
      return response
    })
  }

  public updateStatus(
    id: number,
    status: OperationStatus,
    lostReasons?: Record<string, string>
  ): AxiosPromise<unknown> {
    return this.axios.put(`/operations/${id}/status`, { status, lostReasons })
  }

  public getHistory(id: number): AxiosPromise<OperationStepHistory[]> {
    return this.axios.get(`/operations/${id}/history`)
  }

  public export(filter: OperationFilter) {
    return this.axios.get('/operations/export_to_excel', {
      responseType: 'blob',
      params: { ...filter },
    })
  }

  public changeEntity(filter: OperationFilter, item: { entityId: string }) {
    return this.axios.put(`/operations/entities`, item, { params: { ...filter } })
  }

  public purge(id: number): AxiosPromise {
    return this.axios.post(`/operations/${id}/purge`)
  }

  public setEmmyLotId(id: number, emmyLotId: number): AxiosPromise<void> {
    return this.axios.put('/operations/' + id + '/emmy_lot_id', { emmyLotId })
  }

  public updateInstructor(operationId: number, instructorId: number) {
    return this.axios.put(`/operations/${operationId}/instructor`, { instructorId: instructorId })
  }

  public exportOneToPdf(id: number) {
    return this.axios.get(`/operations/${id}/export_to_pdf`, {
      responseType: 'blob',
    })
  }

  public updateValuation(id: number, request: OperationValuationRequest): AxiosPromise<Operation> {
    return this.axios.put(`/operations/${id}/valuations`, request)
  }

  public findAllExportOperationResultDto(
    filter: OperationFilter,
    pageable: Pageable
  ): AxiosPromise<Page<OperationExportResultDto>> {
    return this.axios.get('/operations', {
      params: { ...filter, ...pageable, dto: 'exportOperationResultDto' },
    })
  }

  public getSummary(filter: OperationFilter): AxiosPromise<OperationSummary> {
    return this.axios.get('/operations/summary', { params: { ...filter } })
  }

  public getSummaryGroupedByStep(filter: OperationFilter): AxiosPromise<OperationSummary> {
    return this.axios.get('/operations/summary/grouped_by_step', { params: { ...filter } })
  }
  public getSummaryGroupedByDelay(filter: OperationFilter): AxiosPromise<OperationSummary> {
    return this.axios.get('/operations/summary/grouped_by_delay', { params: { ...filter } })
  }

  public updateOshPriority(filter: OperationFilter, request: UpdateOperationOshPriorityRequest): AxiosPromise {
    return this.axios.put(
      '/operations/osh_priorities',
      {
        ...request,
      },
      {
        params: { ...filter },
      }
    )
  }

  public exportToControlOrderExcelFile(id: number) {
    return this.axios.get(`/operations/${id}/control_order_excel_file`, {
      responseType: 'blob',
    })
  }
  public updateOshCommentary(id: number, oshCommentary: OshCommentaryType): AxiosPromise {
    return this.axios.put(`/operations/${id}/osh_commentary`, {
      oshCommentary: oshCommentary,
    })
  }

  public updateProperty(id: number, property: Property): AxiosPromise {
    return this.axios.put(`/operations/${id}/property`, property)
  }

  public oshHideOperations(filter: OperationFilter, request: { oshHidden: boolean }): AxiosPromise {
    return this.axios.put(`/operations/osh_hidden`, request, { params: filter })
  }

  public updateSendFinalVersionToBeneficiary(
    id: number,
    request: { sendFinalVersionToBeneficiaryDate?: LocalDate }
  ): AxiosPromise {
    return this.axios.put(`/operations/${id}/send_final_version`, request)
  }

  public updateSentDocumentTypeIds(id: number, documentTypeId: number): AxiosPromise<Operation> {
    return this.axios.put('operations/' + id + '/sent_document_type_ids', {
      documentTypeId,
    })
  }
}
export const operationApi = new OperationApi(axiosInstance)
