<template>
  <VAlert v-if="operation.mustSendFinalVersion" type="info">
    <b v-if="operation.stepId >= 110">La version finale est disponible !</b>
    <b v-else>Les documents de contrôle sont disponibles !</b>
    <br />
    <span v-if="operation.stepId >= 110"
      >Envoyez dès maintenant la VF, en quelques clics via CAPTE (ou confirmez son envoi)</span
    >
    <span v-else
      >Envoyez dès maintenant les documents de contrôle, en quelques clics via CAPTE (ou confirmez son envoi)</span
    >
    <div class="d-flex">
      <CommentaireDialog init="vf" :operation="operation" @send="emit('sent')">
        <template #activator="{ props }">
          <VLink v-if="operation.stepId >= 110" v-bind="props">Envoyer la VF</VLink>
          <VLink v-else v-bind="props">Envoyer les documents de contrôles</VLink>
        </template>
      </CommentaireDialog>
      <div class="px-2">·</div>
      <CardDialog
        v-model:model-value="sendFinalVersionDeclarationDialog.active"
        title="Confirmer l'envoi de la VF"
        max-width="640px"
        :disabled="sendFinalVersionDeclarationDialog.executing"
        card-text-class="gap-4 d-flex flex-column"
      >
        <template #activator="{ props }">
          <VLink v-if="operation.stepId >= 110" v-bind="props">J'ai déjà envoyé la VF</VLink>
          <VLink v-else v-bind="props">J'ai déjà envoyé les documents de contrôles</VLink>
        </template>

        <div>
          Vous confirmer que la VF pour l'<b>opération {{ operation.chronoCode }}</b> a bien été transmise au client. La
          date d'envoi est, par défaut, la date du jour.
        </div>

        <NjDatePicker v-model="sendFinalVersionDeclarationDialog.date" label="Date d'envoi (facultatif)" clearable>
        </NjDatePicker>

        <template #actions="{ modelValue }">
          <NjBtn
            variant="outlined"
            :disabled="sendFinalVersionDeclarationDialog.executing"
            @click="modelValue.value = false"
            >Annuler</NjBtn
          >
          <NjBtn
            color="primary"
            :loading="sendFinalVersionDeclarationDialog.executing"
            @click="sendFinalVersionDeclarationDialog.execute()"
            >Confirmer</NjBtn
          >
        </template>
      </CardDialog>
    </div>
  </VAlert>
</template>

<script lang="ts" setup>
import { useSnackbarStore } from '@/stores/snackbar'
import type { LocalDate } from '@/types/date'
import type { Operation } from '@/types/operation'
import CommentaireDialog from './dialog/CommentaireDialog.vue'

const props = defineProps<{
  operation: Operation
}>()
const emit = defineEmits<{
  sent: []
}>()

const snackbarStore = useSnackbarStore()

const sendFinalVersionDeclarationDialog = ref({
  active: false,
  date: formatLocalDate(new Date()) as LocalDate | undefined,
  executing: false,
  execute: () => {
    sendFinalVersionDeclarationDialog.value.executing = true
    operationApi
      .updateSendFinalVersionToBeneficiary(props.operation.id, {
        sendFinalVersionToBeneficiaryDate: sendFinalVersionDeclarationDialog.value.date,
      })
      .then(() => {
        snackbarStore.setSuccess("Déclaration d'envoi de la VF bien prise en compte")
        sendFinalVersionDeclarationDialog.value.active = false
        emit('sent')
      })
      .catch(async (el) => {
        snackbarStore.setError(await handleAxiosException(el))
      })
      .finally(() => {
        sendFinalVersionDeclarationDialog.value.executing = false
      })
  },
})
</script>
