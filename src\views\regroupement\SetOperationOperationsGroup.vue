<template>
  <VRow>
    <VCol>
      <VRow class="flex-column content-layout">
        <VCol class="content-layout__header"> Opérations disponibles </VCol>
        <VCol class="content-layout__header">
          <VTextField
            v-model="searchInAvailableOperation"
            prepend-inner-icon="mdi-magnify"
            label="Rechercher"
            :loading="availableOperation.loading"
          >
            <template #loader="{ isActive }">
              <VProgressLinear :active="isActive" color="primary" absolute height="4" indeterminate />
            </template>
          </VTextField>
        </VCol>
        <VCol class="content-layout__main">
          <NjDataTable
            :pageable="pageable"
            :page="availableOperation.value!"
            :headers="headers"
            class="table"
            fixed
            @update:pageable="updatePageable"
          >
            <template #[`item.property.code`]="{ item }">
              <VRow>
                <VCol align-self="center">
                  {{ item?.property?.code }}
                </VCol>
                <VCol align="end">
                  <NjIconBtn icon="mdi-plus" rounded="0" @click="addToSelectedOperation(item)" />
                </VCol>
              </VRow>
            </template>
          </NjDataTable>
        </VCol>
      </VRow>
    </VCol>
    <VCol>
      <VRow class="flex-column content-layout">
        <VCol class="content-layout__header"> Opérations sélectionnées </VCol>
        <VCol class="content-layout__header">
          <VTextField
            v-model="searchInOperationsInOperationsGroup"
            prepend-inner-icon="mdi-magnify"
            label="Rechercher"
            :loading="operationInOperationsGroup.loading"
          >
            <template #loader="{ isActive }">
              <VProgressLinear :active="isActive" color="primary" absolute height="4" indeterminate />
            </template>
          </VTextField>
        </VCol>
        <VCol class="content-layout__main">
          <NjDataTable
            :pageable="pageableOperationsGroup"
            :page="operationInOperationsGroup.value"
            :headers="headers"
            class="table"
            fixed
            @update:pageable="updatePageableOperationGroup"
          >
            <template #[`item.property.code`]="{ item }">
              <VRow>
                <VCol align-self="center">
                  {{ item?.property?.code }}
                </VCol>
                <VCol align="end">
                  <NjIconBtn icon="mdi-minus" rounded="0" @click="removeFromSelectedOperation(item)" />
                </VCol>
              </VRow>
            </template>
          </NjDataTable>
        </VCol>
      </VRow>
    </VCol>
  </VRow>
</template>
<script setup lang="ts">
import { useSnackbarStore } from '@/stores/snackbar'
import type { OperationsGroup } from '@/types/operationsGroup'
import type { Operation } from '@/types/operation'
import { debounce } from 'lodash'
import type { PropType } from 'vue'
import { useDialogStore } from '@/stores/dialog'

const props = defineProps({
  operationsGroup: {
    type: Object as PropType<OperationsGroup>,
    required: true,
  },
})

const dialogStore = useDialogStore()

const headers = [
  {
    title: 'Nom',
    value: 'operationName',
  },
  {
    title: 'Chrono',
    value: 'chronoCode',
  },
  {
    title: 'Code',
    value: 'standardizedOperationSheet.operationCode',
  },
  {
    title: 'Numéro installation',
    value: 'property.code',
  },
]

//Available operation
const searchInAvailableOperation = ref()
const {
  data: availableOperation,
  pageFilter,
  pageable,
  updatePageable,
  reload,
} = usePagination<Operation>(
  (filter, pageable) => operationApi.findAll(filter, pageable),
  {
    entityIds: [props.operationsGroup.entity.id],
    stepIds: [10, 20, 30],
    inOperationsGroup: false,
    beneficiaryId: props.operationsGroup.beneficiary?.id,
    operationStatuses: ['DOING'],
    hasEpcBonus: props.operationsGroup.onlyEpcOperations,
  },
  {}
)

const debounceSearchInAvailableOperation = debounce((v: string | undefined) => {
  pageFilter.value.search = v
  reload()
}, 300)

watch(searchInAvailableOperation, (v) => {
  availableOperation.value.loading = true
  debounceSearchInAvailableOperation(v)
})

//operation in operationgroup

const searchInOperationsInOperationsGroup = ref<string>('')

const debounceSearchInOperationsInOperationsGroup = debounce((v: string) => {
  pageFilterOperationsGroup.value.search = v
  reloadOperationsGroup()
}, 300)

watch(searchInOperationsInOperationsGroup, (v) => {
  operationInOperationsGroup.value.loading = true
  debounceSearchInOperationsInOperationsGroup(v)
})

const {
  data: operationInOperationsGroup,
  pageFilter: pageFilterOperationsGroup,
  pageable: pageableOperationsGroup,
  updatePageable: updatePageableOperationGroup,
  reload: reloadOperationsGroup,
} = usePagination<Operation>(
  (filter, pageable) => operationApi.findAll(filter, pageable),
  {
    operationsGroupId: props.operationsGroup.id,
  },
  {}
)

const promisableOperation = ref(emptyValue<Operation>())
const snackbarStore = useSnackbarStore()
const addToSelectedOperation = async (item: Operation) => {
  if (
    (item.atypicalClassicValuationValue != null || item.atypicalPrecariousnessValuationValue != null) &&
    (props.operationsGroup.atypicalClassicValuationValue != item.atypicalClassicValuationValue ||
      props.operationsGroup.atypicalPrecariousnessValuationValue != item.atypicalPrecariousnessValuationValue)
  ) {
    if (
      !(await dialogStore.addAlert({
        title: 'Valorisation atypique différente du regroupement',
        message: `L'opération ${item.chronoCode} a une valorisation atypique (Cla : ${item.atypicalClassicValuationValue} €/MWhc / Préca : ${item.atypicalPrecariousnessValuationValue} €/MWhc) différente de celui du regroupement (Cla : ${props.operationsGroup.atypicalClassicValuationValue != null ? props.operationsGroup.atypicalClassicValuationValue + ' €/MWhc' : 'Aucun'} / Préca : ${props.operationsGroup.atypicalPrecariousnessValuationValue != null ? props.operationsGroup.atypicalPrecariousnessValuationValue + ' €/MWhc' : 'Aucun'}).\n\nLa valorisation atypique du regroupement écrasera celle de l'opération.\nVous devez faire la demande de valorisation atypique au niveau du regroupement.\n\nÊtes-vous sûr de vouloir continuer ?`,
        maxWidth: '640px',
      }))
    ) {
      return
    }
  }
  const request = mapToOperationRequest(item)
  request.atypicalClassicValuationValue = props.operationsGroup.atypicalClassicValuationValue ?? undefined
  request.atypicalPrecariousnessValuationValue = props.operationsGroup.atypicalPrecariousnessValuationValue ?? undefined
  request.operationsGroupId = props.operationsGroup.id
  handleAxiosPromise(promisableOperation, simulationApi.updateSimulation(item.id, request), {
    afterSuccess: () => {
      reload()
      reloadOperationsGroup()
    },
    afterError: () => {
      snackbarStore.setError(
        promisableOperation.value.error ?? "Erreur lors de l'ajout de l'opération au regroupement",
        5000
      )
    },
  })
}

const removeFromSelectedOperation = async (item: Operation) => {
  if (
    await dialogStore.addAlert({
      title: "Retirer l'opération",
      message:
        "Attention! En sortant cette opération du regroupement, tous les documents présents au niveau de regroupement seront par conséquent retirés de l'opération.\n" +
        (item.operationsGroup?.atypicalClassicValuationValue != null ||
        item.operationsGroup?.atypicalPrecariousnessValuationValue != null
          ? "De plus, en sortant l'opération du regroumement, sa valorisation atypique (issue du regroupement) sera annulée. Une nouvelle demande devra alors être faite.\n"
          : '') +
        'Voulez-vous continuer?',
      maxWidth: '640px',
    })
  ) {
    operationsGroupApi
      .removeOperation(item.id)
      .then(() => {
        reload()
        reloadOperationsGroup()
      })
      .catch(async (err) => {
        snackbarStore.setError(
          (await handleAxiosException(err)) ??
            'Une erreur est survenue en tentant de retirer une opération du regroupement'
        )
      })
  }
}
</script>
