<template>
  <component
    :is="requestFilter || $attrs.to ? VCard : VSheet"
    class="dashboard-card-number d-flex flex-column"
    :color="selected ? '#E7EEFC' : undefined"
    position="relative"
    :border="selected ? 'primary left s-lg opacity-100' : 'primary left s-lg opacity-0'"
    :disabled
    @click="onClickCard"
  >
    <div class="d-flex align-center">
      <div class="dashboard-card-number__title">
        <slot name="title">
          {{ title }}
        </slot>
      </div>
      <!-- <NjIconBtn size="small" icon="mdi-refresh" density="compact" @click="load(true)"></NjIconBtn> -->
      <slot name="after-title">
        <!-- A laisser car pratique pour debug -->
        <VTooltip v-if="tooltip" max-width="320">
          <template #activator="{ props }">
            <VIcon
              icon="mdi-information-outline"
              v-bind="props"
              size="x-small"
              :color="selected ? 'primary' : '#60798B'"
            />
          </template>
          {{ tooltip }}
        </VTooltip>
      </slot>
    </div>
    <div class="dashboard-card-number__subtitle">{{ subtitle }}</div>
    <VSpacer />
    <VAlert v-if="error" type="error">{{ error }}</VAlert>
    <slot v-else name="value" :value="localModelValue" :loading="currentLoading">
      <div class="dashboard-card-number__value-line">
        <template v-if="disabled"> - </template>
        <template v-else-if="value !== undefined">
          <div class="dashboard-card-number__value" :class="{ 'text-primary': selected }">
            {{ Math.round(localNumberModelValue) }}
          </div>
        </template>
        <slot name="action" :props="{ class: 'dashboard-card-number__action' }">
          <template v-if="requestFilter">
            <VLink v-if="!selected" append-icon="mdi-arrow-down" class="dashboard-card-number__action"
              >Voir la liste</VLink
            >
            <VLink v-else append-icon="mdi-close" class="">Effacer</VLink>
          </template>
        </slot>
      </div>
    </slot>
  </component>
</template>

<script setup lang="ts">
import { VAlert, VCard, VSheet } from 'vuetify/components'
import { dashboardCardKey, dashboardDataKey } from './keys'
import { gsap } from 'gsap'
import type { OperationFilter } from '@/api/operation'
import { cloneDeep, isEqual } from 'lodash'
import { trace } from '@/stores/analytics'

const loading = defineModel<boolean>('loading')

const selected = defineModel<boolean>('selected', {
  default: false,
})

const props = defineProps<{
  title: string
  subtitle?: string
  value?: number | ((filter: OperationFilter) => Promise<any>)
  tooltip?: string
  disabled?: boolean
  requestFilter?: OperationFilter
  traceId?: string
}>()

const { loading: globalLoading, filter: filter } = inject(dashboardDataKey, {
  loading: ref(false),
  filter: ref<OperationFilter>({}),
  dashboardFilter: ref<OperationFilter>({}),
})

let oldFilter: OperationFilter = {}

watch(
  filter,
  () => {
    load()
  },
  {
    deep: true,
  }
)

const load = (force = false) => {
  if (!isEqual(filter.value, oldFilter) || force) {
    oldFilter = cloneDeep(filter.value)
    loading.value = true
    error.value = ''
    if (typeof props.value === 'function') {
      props
        .value(filter.value)
        .then((it) => {
          if (typeof it === 'number') {
            nextTick(() => {
              gsap.to(localNumberModelValue, {
                value: it,
                duration: 0.5,
              })
            })
          } else {
            localModelValue.value = it
          }
        })
        .catch(async (e: unknown) => {
          error.value = await handleAxiosException(e)
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      nextTick(() => {
        gsap.to(localNumberModelValue, {
          value: props.value,
          duration: 1,
        })
      })
    }
  }
}

const localNumberModelValue = ref(0)
const localModelValue = ref()
const error = ref('')

const currentLoading = computed(() => {
  return globalLoading.value || loading.value
})

const toggleFilter = () => {
  if (props.requestFilter) {
    if (selected.value) {
      Object.keys(props.requestFilter).forEach((key) => {
        delete filter.value[key as keyof typeof filter.value]
        if (key === 'operationStatuses') {
          filter.value[key] = ['DOING']
        }
      })
    } else {
      filter.value = { ...filter.value, ...cloneDeep(props.requestFilter) }
    }
    selected.value = !selected.value
  }
}

const onClickCard = () => {
  if (props.traceId) {
    trace('number_card_click', { traceId: props.traceId, action: !selected.value ? 'select' : 'unselect' })
  }
  if (props.requestFilter) {
    clickCard()
  }
}
const clickCard = () => {
  toggleFilter()
}
watch(
  () => props.value,
  () => {
    load()
  },
  {
    immediate: true,
  }
)

provide(dashboardCardKey, {
  selected,
  disabled: computed(() => props.disabled),
})
</script>

<style lang="scss">
.dashboard-card-number {
  padding: 16px;
  background-color: white;

  &__title {
    font-size: 1.125rem;
    flex-grow: 1;
    display: flex;
    align-items: center;
  }
  &__subtitle {
    font-size: 0.75rem;
    color: #60798b;
  }
  &__value {
    font-size: 1.5rem;
    font-weight: 700;
    flex-grow: 1;
  }

  &__value-line {
    display: flex;
    align-items: end;
  }

  &__action {
    transition: 0.3s ease-out all;
    opacity: 0;
  }

  &:hover {
    .dashboard-card-number__action {
      opacity: 1;
    }
  }
}
</style>
