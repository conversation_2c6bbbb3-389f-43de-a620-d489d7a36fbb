include:
  - project: 'INFRA/Adc/gitlab-admin/runners-template'
    ref: 'master'
    file: 'base-template.yml'

stages:
  - prepare     # Configuration, dependencies download
  - code_quality
  - check       # Code style and unit tests
  - build_rec
  - deploy_rec
  - build_ppr
  - deploy_ppr
  - build_prd
  - deploy_prd

variables:
  MIN_SCORE: 10
  VERSION: 1.0.0.${CI_PIPELINE_IID}
  APIZI_DEPLOY_URL: https://apis.svc.engie-solutions.net/apizi_deploy/api/v1
  SRC_ROOT:
    description: "The path to package.json file"
    value: "."
  NODE_VERSION:
    description: "The project target node version"
    value: "18"
  REGISTRY:
    description: "Registry proxy of company"
    value: "https://nexus.engie-solutions.net/repository/npm-proxy"
  SSH_REMOTE_URL:
    description: "URL du repo pour pouvoir fetch et push des branches"
    value: "******************************:systems/capte/capte-spa.git"

  NODE_OPTIONS: "--max-old-space-size=1843" ## 90% de ce qui est dédié au runner

  NEXUS_PATH_RELEASE: https://nexus.engie-solutions.net/repository/engie-cofely-releases/com/engie/solutions/systems
  NEXUS_PATH_SNAPSHOT: https://nexus.engie-solutions.net/repository/engie-cofely-snapshots/com/engie/solutions/systems
  WORKFLOW_TRIGGER_URL: https://prod-24.westeurope.logic.azure.com:443/workflows/826522bdee9f4085bb242c039928d7bb/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=XgXnuwKoRaSBRP53JF-TXm4CpcrbZcdNR5aM-HVzoQU

  POLICY: pull

default:
  tags:
    - ${TPL_NODEJS_18_SYS_PRD}

cache:
  key:
    files:
      - pnpm-lock.yaml
  paths:
    - .pnpm-store
  policy: $POLICY

before_script:
  - cd ${SRC_ROOT}
  - "echo PIPELINE_IID : ${CI_PIPELINE_IID}"
  - pnpm config set store-dir .pnpm-store

prepare:
  stage: prepare
  script:
#    - nvm use ${NODE_VERSION}
    - git --version
    - node --version
    - pnpm --version
    - cd ${SRC_ROOT}
    - pwd
#    - pnpm config set "strict-ssl" false
    - pnpm config set registry ${REGISTRY}
#    - pnpm config set disable-self-update-check true
#    -
#    - pnpm config list
    - pnpm install --frozen-lockfile
#    - export VERSION=$(node -p "require('./package.json').version")
#    - echo "Project version = ${VERSION}"
    - du -sh node_modules
  variables:
    POLICY: pull-push
  rules:
    - if: $CI_MERGE_REQUEST_ID               # Execute jobs in merge request context
    - if: $CI_COMMIT_BRANCH == 'dev'         # Execute jobs when a new commit is pushed to dev branch
    - if: $CI_COMMIT_BRANCH == 'hotfix'         # Execute jobs when a new commit is pushed to dev branch

check:
  stage: check
  script:
#    - nvm use ${NODE_VERSION}
#    - echo $NODE_OPTIONS
#    - cat /proc/meminfo
#    - grep MemTotal /proc/meminfo | awk '{print $2}'
    - cd ${SRC_ROOT}
    - pnpm config set registry ${REGISTRY}
    - pnpm install --frozen-lockfile
    - pnpm build-only # Obligé de le faire avant le lint à cause de fichier généré pour eslint avec unplug-auto-import
    - pnpm verify

  rules:
    - if: $CI_MERGE_REQUEST_ID               # Execute jobs in merge request context
    - if: $CI_COMMIT_BRANCH == 'dev'         # Execute jobs when a new commit is pushed to dev branch
    - if: $CI_COMMIT_BRANCH == 'hotfix'         # Execute jobs when a new commit is pushed to dev branch
  artifacts:
    reports:
      codequality: gl-code-quality-report.json


build_ppr:
  stage: build_ppr
  variables:
    DEPLOY_ENV: ppr
  environment:
    name: ppr
  script: |
    pnpm config set registry ${REGISTRY}
    pnpm install --frozen-lockfile
    echo "VITE_APP_VERSION=$VERSION
    VITE_BUILD_DATE=$(date -Iseconds)
    VITE_BUILD_COMMIT=$(git rev-parse --short HEAD)" > .env.local
    pnpm build-only --mode=${DEPLOY_ENV}
    zip -r dist.zip dist
    
    F=dist.zip
    A=${CI_PROJECT_NAME}-${DEPLOY_ENV}
    V=$VERSION
    T=RELEASE
    REPO=engie-cofely-releases
  
    if [ "$T" == 'SNAPSHOT' ]
    then
      REPO=engie-cofely-snapshots
      V="1.0.0-SNAPSHOT"
      echo "SNAPSHOT : Version is 1.0.0-SNAPSHOT + timestamp"
    fi
    
    echo "Publish $F to NEXUS : $A $V $T"
    echo mvn deploy:deploy-file -DgroupId=com.engie.solutions.systems -DartifactId="$A" -Dversion="$V" -DgeneratePom=true -Dpackaging=zip -Durl=https://nexus.engie-solutions.net/repository/$REPO -Dfile="$F" -DrepositoryId=nexus-$REPO
    mvn deploy:deploy-file -DgroupId=com.engie.solutions.systems -DartifactId="$A" -Dversion="$V" -DgeneratePom=true -Dpackaging=zip -Durl=https://nexus.engie-solutions.net/repository/$REPO -Dfile="$F" -DrepositoryId=nexus-$REPO
    
    if [ $? -ne 0 ]
    then
    echo "Unable to upload to Nexus (mvn failed)"
    exit 2
    fi
    
    exit 0
  rules:
    - if: $CI_COMMIT_BRANCH == 'deploy/ppr'
    - if: $CI_COMMIT_BRANCH == 'dev'
      when: manual
    - if: $CI_COMMIT_BRANCH == 'hotfix'
      when: manual

build_rec:
  stage: build_rec
  variables:
    DEPLOY_ENV: rec
  environment:
    name: rec
  script: |
    pnpm config set registry ${REGISTRY}
    pnpm install --frozen-lockfile
    echo "VITE_APP_VERSION=$VERSION
    VITE_BUILD_DATE=$(date -Iseconds)
    VITE_BUILD_COMMIT=$(git rev-parse --short HEAD)" > .env.local
    pnpm build-only --mode=${DEPLOY_ENV}
    zip -r dist.zip dist
    
    F=dist.zip
    A=${CI_PROJECT_NAME}-${DEPLOY_ENV}
    V=$VERSION
    T=RELEASE
    REPO=engie-cofely-releases
  
    if [ "$T" == 'SNAPSHOT' ]
    then
      REPO=engie-cofely-snapshots
      V="1.0.0-SNAPSHOT"
      echo "SNAPSHOT : Version is 1.0.0-SNAPSHOT + timestamp"
    fi
    
    echo "Publish $F to NEXUS : $A $V $T"
    echo mvn deploy:deploy-file -DgroupId=com.engie.solutions.systems -DartifactId="$A" -Dversion="$V" -DgeneratePom=true -Dpackaging=zip -Durl=https://nexus.engie-solutions.net/repository/$REPO -Dfile="$F" -DrepositoryId=nexus-$REPO
    mvn deploy:deploy-file -DgroupId=com.engie.solutions.systems -DartifactId="$A" -Dversion="$V" -DgeneratePom=true -Dpackaging=zip -Durl=https://nexus.engie-solutions.net/repository/$REPO -Dfile="$F" -DrepositoryId=nexus-$REPO
    
    if [ $? -ne 0 ]
    then
    echo "Unable to upload to Nexus (mvn failed)"
    exit 2
    fi
    
    exit 0
  rules:
    - if: $CI_COMMIT_BRANCH == 'deploy/rec'
    - if: $CI_COMMIT_BRANCH == 'dev'
      when: manual
#    - if: $CI_COMMIT_BRANCH == 'hotfix'
#      when: manual

deploy_ppr:
  stage: deploy_ppr
  variables:
    DEPLOY_ENV: ppr
    PROJECT_NAME_IN_NEXUS: ${CI_PROJECT_NAME}-${DEPLOY_ENV}
    NEXUS_PATH: $NEXUS_PATH_RELEASE
  environment:
    name: ppr
  script:
    - chmod +x ${CI_PROJECT_DIR}/ci/deploy_package.sh
    - ${CI_PROJECT_DIR}/ci/deploy_package.sh
  rules:
    - if: $CI_COMMIT_BRANCH == 'deploy/ppr'
    - if: $CI_COMMIT_BRANCH == 'dev'
    - if: $CI_COMMIT_BRANCH == 'hotfix'

deploy_rec:
  stage: deploy_rec
  variables:
    DEPLOY_ENV: rec
    PROJECT_NAME_IN_NEXUS: ${CI_PROJECT_NAME}-${DEPLOY_ENV}
    NEXUS_PATH: $NEXUS_PATH_RELEASE
  environment:
    name: rec
  script:
    - chmod +x ${CI_PROJECT_DIR}/ci/deploy_package.sh
    - ${CI_PROJECT_DIR}/ci/deploy_package.sh
  rules:
    - if: $CI_COMMIT_BRANCH == 'deploy/rec'
    - if: $CI_COMMIT_BRANCH == 'dev'
#    - if: $CI_COMMIT_BRANCH == 'hotfix'

build_prd:
  stage: build_prd
  variables:
    DEPLOY_ENV: prd
  script: |
    pnpm config set registry ${REGISTRY}
    pnpm install --frozen-lockfile
    echo "VITE_APP_VERSION=$VERSION
    VITE_BUILD_DATE=$(date -Iseconds)
    VITE_BUILD_COMMIT=$(git rev-parse --short HEAD)" > .env.local
    pnpm build-only --mode=${DEPLOY_ENV}
    zip -r dist.zip dist
    
    F=dist.zip
    A=${CI_PROJECT_NAME}-${DEPLOY_ENV}
    V=$VERSION
    T=RELEASE
    REPO=engie-cofely-releases
  
    if [ "$T" == 'SNAPSHOT' ]
    then
      REPO=engie-cofely-snapshots
      V="1.0.0-SNAPSHOT"
      echo "SNAPSHOT : Version is 1.0.0-SNAPSHOT + timestamp"
    fi
    
    echo "Publish $F to NEXUS : $A $V $T"
    echo mvn deploy:deploy-file -DgroupId=com.engie.solutions.systems -DartifactId="$A" -Dversion="$V" -DgeneratePom=true -Dpackaging=zip -Durl=https://nexus.engie-solutions.net/repository/$REPO -Dfile="$F" -DrepositoryId=nexus-$REPO
    mvn deploy:deploy-file -DgroupId=com.engie.solutions.systems -DartifactId="$A" -Dversion="$V" -DgeneratePom=true -Dpackaging=zip -Durl=https://nexus.engie-solutions.net/repository/$REPO -Dfile="$F" -DrepositoryId=nexus-$REPO
    
    if [ $? -ne 0 ]
    then
    echo "Unable to upload to Nexus (mvn failed)"
    exit 2
    fi
    
    exit 0
  rules:
    - if: $CI_COMMIT_BRANCH == 'deploy/prd'
    - if: $CI_COMMIT_BRANCH == 'dev'
      when: manual
    - if: $CI_COMMIT_BRANCH == 'hotfix'
      when: manual


deploy_prd:
  stage: deploy_prd
  variables:
    DEPLOY_ENV: prd
    PROJECT_NAME_IN_NEXUS: ${CI_PROJECT_NAME}-${DEPLOY_ENV}
    NEXUS_PATH: $NEXUS_PATH_RELEASE
  script:
    - chmod +x ${CI_PROJECT_DIR}/ci/deploy_package.sh
    - ${CI_PROJECT_DIR}/ci/deploy_package.sh
  rules:
    - if: $CI_COMMIT_BRANCH == 'deploy/prd'
    - if: $CI_COMMIT_BRANCH == 'dev'
    - if: $CI_COMMIT_BRANCH == 'hotfix'
