<template>
  <div class="nj-text-field nj-field">
    <label><VIcon v-if="locked" icon="mdi-lock" size="x-small" /> {{ $attrs.label }}</label>
    <VTextField
      v-bind="{
        ...$attrs,
        label: undefined,
        class: undefined,
        style: undefined,
        rules: flatten([$attrs.rules ?? [], required ? [requiredRule] : []]) as ValidationRule[],
      }"
      :readonly="!!locked || ($attrs.readonly as boolean)"
      :class="fieldClass"
      :style="fieldStyle"
      :bg-color="locked || readonly ? '#F6F8F9' : undefined"
    >
      <template v-for="(_, slot) of $slots as {}" #[slot]="scope">
        <slot :name="slot" v-bind="scope as any" />
      </template>
      <!-- <template v-if="locked" #prepend-inner>
        <div class="nj-field__locked-prepend">
          <VIcon icon="mdi-lock" size="x-small" />
        </div>
      </template> -->
      <template v-if="required" #prepend-inner>
        <div class="nj-field__required-prepend"><VIcon icon="mdi-asterisk" size="x-small" /></div>
      </template>
      <template v-else-if="recommended" #prepend-inner>
        <div class="nj-field__recommended-prepend">✦</div>
      </template>
      <template v-else #prepend-inner>
        <slot name="prepend-inner"></slot>
      </template>
    </VTextField>
  </div>
</template>

<script setup lang="ts">
import { VTextField } from 'vuetify/components'
import { requiredRule, type ValidationRule } from '@/types/rule'
import { flatten } from 'lodash'

defineProps<{
  required?: boolean
  recommended?: boolean
  fieldClass?: any
  fieldStyle?: any
  locked?: boolean | string
  readonly?: boolean | string
}>()
// const props = defineProps<typeof VTextField['$props']>()
// props.
</script>

<style lang="scss">
.nj-field {
  .v-field--prepended {
    padding-inline-start: 0px;
  }

  &__required-prepend {
    background-color: #e7eefc;
    color: #007acd;
    height: 100%;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 4px;
  }

  &__recommended-prepend {
    background-color: #f2ecf7;
    color: #744299;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 4px;
  }
}
</style>
