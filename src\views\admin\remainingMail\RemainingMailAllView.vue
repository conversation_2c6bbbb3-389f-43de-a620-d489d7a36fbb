<template>
  <NjPage title="Mails en attente d'envoi" expend-body v-bind="$attrs">
    <template #body>
      <NjDataTable
        :headers="headers"
        :pageable="pageable"
        :page="data.value"
        fixed
        class="w-100"
        @update:pageable="updatePageable"
      >
        <template #[`item.actions`]="{ item }">
          <NjIconBtn icon="mdi-open-in-new" size="small" color="primary" @click.stop="showEmail(item)" />
        </template>
        <template #[`item.documents`]="{ item }">
          <VTooltip v-if="item.documents.length" location="top">
            <template #activator="{ props }">
              <span v-bind="props"> {{ item.documents.length }} documents </span>
            </template>
            <ul>
              <li v-for="doc in item.documents" :key="doc.id">{{ doc.originalFilename }}</li>
            </ul>
          </VTooltip>
          <span v-else> {{ item.documents.length }} documents </span>
        </template>
      </NjDataTable>
    </template>
  </NjPage>
</template>
<script lang="ts" setup>
import { type EntityFilter } from '@/api/entity'
import NjIconBtn from '@/components/NjIconBtn.vue'
import NjPage from '@/components/NjPage.vue'
import type { DataTableHeader } from '@/components/okta/NjDataTable.type'
import NjDataTable from '@/components/okta/NjDataTable.vue'
import type { RemainingMail } from '@/types/remainingMail'
import DOMPurify from 'dompurify'

const search = ref('')
const active = ref(true)
const visible = ref(true)

const { data, pageable, updatePageable } = usePaginationInQuery(
  (filter, pageable) => remainingMailApi.getAll(pageable, {}),
  {
    defaultPageFilter: {
      enabled: active.value,
      visible: visible.value,
      search: search.value,
    } as EntityFilter,
    defaultPageablePartial: {
      page: 0,
      size: 20,
      sort: ['creationDateTime'],
    },
    saveFiltersName: 'RemainingMailAllView',
  }
)

const headers: DataTableHeader[] = [
  {
    title: 'Destinataires',
    value: 'recipients',
    maxLength: 100,
  },
  {
    title: 'CC.',
    value: 'cc',
    maxLength: 100,
  },
  {
    title: 'Sujet du mail',
    value: 'subject',
  },
  {
    title: "Date d'insertion",
    value: 'creationDateTime',
    formater: (_, value) => formatHumanReadableLocalDateTime(value),
  },
  {
    title: 'Documents',
    value: 'documents',
  },
  {
    title: 'Actions',
    value: 'actions',
  },
]

const showEmail = (email: RemainingMail) => {
  const newWindow = window.open(
    'about:blank',
    'Aperçu Mail',
    'resizable=no, toolbar=no, scrollbars=no, menubar=no, status=no, directories=no,location=no'
  )
  newWindow?.document.write(DOMPurify.sanitize(email.body))
}
</script>
