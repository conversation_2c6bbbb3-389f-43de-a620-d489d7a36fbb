<template>
  <div class="d-flex w-100">
    <div class="d-flex flex-column flex-grow-1 flex-basis-0" style="overflow: auto">
      <slot name="header">
        <VContainer
          v-if="title !== false || $slots?.['sub-header'] || showError"
          fluid
          class="nj-page__shadowable pb-2"
          :class="{ 'nj-page--scrolling': !arrivedState.top }"
          style="z-index: 5"
        >
          <VRow v-if="title !== false" style="flex-grow: 0" no-gutters>
            <VCol v-if="display.lgAndUp && canGoBack" class="pb-0" cols="12">
              <VLink icon="mdi-arrow-left" @click="clickGoBack">Retour</VLink>
            </VCol>
            <VCol>
              <div v-if="isString(title)" class="d-flex align-center">
                <h1 style="font-size: 1.5rem">{{ title }}</h1>
                <div class="ml-2">
                  <slot name="after-title"></slot>
                </div>
                <VProgressCircular v-if="loading" color="primary" class="ms-4" indeterminate />
                <VSpacer />
                <slot name="end-title" />
              </div>
              <slot name="subtitle"></slot>
            </VCol>
            <VCol class="flex-grow-0 d-flex" align-self="center" style="gap: 16px">
              <slot name="header-actions" />
            </VCol>
          </VRow>

          <slot name="sub-header"></slot>

          <VRow v-if="showError">
            <VCol>
              <ErrorAlert closable :show="showError" :message="errorMessage" @close="showError = false" />
            </VCol>
          </VRow>
        </VContainer>
      </slot>

      <VContainer
        ref="mainRef"
        fluid
        style="flex-grow: 1; overflow-y: auto; flex-basis: 0"
        :class="{ 'd-flex': expendBody, 'align-stretch': expendBody }"
      >
        <slot name="body" />
      </VContainer>

      <slot name="footer" />
    </div>
    <slot name="drawer" />
  </div>
</template>

<script setup lang="ts">
import { useScroll } from '@vueuse/core'
import type { PropType } from 'vue'
import type { RouteLocationRaw } from 'vue-router'
import type { VRow } from 'vuetify/components'
import ErrorAlert from './ErrorAlert.vue'
import VLink from './VLink.vue'
import { isString } from 'lodash'
import { useDisplay } from 'vuetify'

const props = defineProps({
  title: {
    type: [Boolean, String],
    default: false,
  },
  canGoBack: {
    type: [Boolean, Object, Function] as PropType<boolean | RouteLocationRaw | (() => void)>,
  },
  loading: Boolean,
  errorMessage: {
    type: String,
  },
  expendBody: Boolean,
})
const display = useDisplay()

const showError = ref(false)
watch(
  () => props.errorMessage,
  (v) => {
    if (v) {
      showError.value = true
    } else {
      showError.value = false
    }
  }
)
const router = useRouter()

const mainRef = ref<VRow | null>(null)
const elMain = computed(() => mainRef.value?.$el)
const { arrivedState } = useScroll(elMain)

const clickGoBack = () => {
  if (props.canGoBack !== undefined) {
    if (typeof props.canGoBack === 'boolean') {
      if (props.canGoBack) {
        router.back()
      }
    } else if (typeof props.canGoBack === 'object' || typeof props.canGoBack === 'string') {
      router.push(props.canGoBack)
    } else {
      props.canGoBack()
    }
  }
}
</script>

<style lang="scss">
.nj-page {
  &__shadowable {
    transition: all 0.3s ease-in;
  }

  &--scrolling {
    box-shadow: 0px 0px 16px 8px rgba(0, 0, 0, 0.5);
  }
}
</style>
