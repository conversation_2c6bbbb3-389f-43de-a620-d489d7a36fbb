<template>
  <CardDialog
    :model-value="modelValue"
    title="Choix d'un syndicat de copropriété"
    fixed
    :max-width="manualInputs ? 640 : undefined"
    @update:model-value="modelValue = $event"
  >
    <VRow v-if="manualInputs" class="flex-column" justify="center" align="center" dense>
      <VCol>
        <VAlert type="warning"
          >En ajoutant un syndicat de copropriété, cela forcera le nom de l'opération à être celui du syndicat.</VAlert
        >
      </VCol>
      <VCol class="">
        Notre outil de recherches sur les syndicats de copropriété est en maintenance. <br />
        Veuillez saisir manuellement les informations de votre syndic, en vous référant aux informations données par
        votre client et/ou en consulant l'annuaire des copropriétés.
      </VCol>
      <VCol class="text-center">
        <VLink
          color="primary"
          append-icon="mdi-open-in-new"
          target="_blank"
          href="https://www.registre-coproprietes.gouv.fr/annuaire"
          class="mt-4"
          variant="flat"
          rounded="0"
        >
          Consulter « l'Annuaire des copropriétés »
        </VLink>

        <br />
      </VCol>
      <VCol>
        <VCard
          class="pa-4"
          style="border: 1px solid #ccc; background-color: #f5f5f5"
          @click="addToClipBoard(props.address ? formatAddressable(props.address) : '', 'Adresse copié')"
        >
          {{ formatAddressable(props.address) }}
          <VIcon icon="mdi-content-copy" color="primary" class="me-2" /> Adresse de l'installation :
        </VCard>
      </VCol>
      <VCol>
        <div class="text-caption">Exemple de résultat de recherche avec indications :</div>
        <VImg
          src="@/assets/aide_copie.png"
          alt="Indications sur les informations à copier sur l'Annuaire des copropriétés"
          style="border: 1px solid #ccc"
          height="240px"
          leg
        />
      </VCol>
      <VCol>
        <NjTextField
          v-model="manualInfos.registrationNumber"
          label="Numéro d'immatriculation du syndicat de copropriété"
        />
      </VCol>
      <VCol>
        <NjTextField v-model="manualInfos.usageName" label="Nom du syndicat de copropriété" />
      </VCol>
    </VRow>
    <VRow v-else class="flex-column h-100">
      <VCol class="flex-grow-0">
        <VForm ref="manualInfosForm">
          <VRow>
            <VCol cols="5">
              <SearchInput
                v-model="searchAddress.street"
                :loading="coOwnerShipSyndicateLoading"
                label="Numéro et Voie"
                no-prepend-icon
              />
            </VCol>
            <VCol>
              <SearchInput
                v-model="searchAddress.postalCode"
                :loading="coOwnerShipSyndicateLoading"
                label="Code Postal"
                no-prepend-icon
              />
            </VCol>
            <VCol>
              <SearchInput
                v-model="searchAddress.city"
                :loading="coOwnerShipSyndicateLoading"
                label="Commune"
                no-prepend-icon
              />
            </VCol>
          </VRow>
        </VForm>
      </VCol>
      <VCol>
        <CoOwnerShipSyndicateAllView
          v-model:selections="selectedCoOwnerShipSyndicate"
          v-model:loading="coOwnerShipSyndicateLoading"
          :address="searchAddress"
        />
      </VCol>
    </VRow>
    <template #actions>
      <NjBtn variant="outlined" @click="closeCoOwnerShipSyndicateDialog">Annuler</NjBtn>
      <NjBtn :disabled="manualInputs ? false : !selectedCoOwnerShipSyndicate[0]" @click="selectCoOwnerShipSyndicate"
        >Valider</NjBtn
      >
    </template>
    <template v-if="$slots['activator'] !== undefined" #activator="scope">
      <slot name="activator" v-bind="scope"></slot>
    </template>
  </CardDialog>
</template>

<script setup lang="ts">
import { type Address, formatAddressable } from '@/types/address'
import type { CoOwnerShipSyndicate } from '@/types/coOwnerShipSyndicate'
import type { PropType } from 'vue'
import CoOwnerShipSyndicateAllView from '../coOwnerShipSyndicateAllView.vue'
import { addToClipBoard } from '@/types/clipboard'

const modelValue = defineModel<boolean>({ default: false })
const selected = defineModel<CoOwnerShipSyndicate[]>('selected', { default: () => [] })

const props = defineProps({
  address: {
    type: Object as PropType<Address>,
    required: false,
  },
})

const searchAddress = ref<Address>({
  street: '',
  postalCode: '',
  city: '',
  country: null,
})

const manualInfos = ref({
  registrationNumber: '',
  usageName: '',
})

const selectedCoOwnerShipSyndicate = ref<CoOwnerShipSyndicate[]>([])
const coOwnerShipSyndicateLoading = ref(false)

const initializeSearchFields = () => {
  if (manualInputs.value) {
    manualInfos.value.registrationNumber = selected.value[0]?.registrationNumber || ''
    manualInfos.value.usageName = selected.value[0]?.usageName || ''
  } else if (props.address) {
    searchAddress.value.street = props.address.street || ''
    searchAddress.value.postalCode = props.address.postalCode || ''
    searchAddress.value.city = props.address.city || ''
  } else {
    searchAddress.value.street = ''
    searchAddress.value.postalCode = ''
    searchAddress.value.city = ''
  }
}

watch(
  () => modelValue.value,
  (v) => {
    if (v) {
      initializeSearchFields()
      selectedCoOwnerShipSyndicate.value = []
    }
  }
)

watch(
  searchAddress,
  () => {
    coOwnerShipSyndicateLoading.value = true
  },
  { deep: true }
)

const closeCoOwnerShipSyndicateDialog = () => {
  modelValue.value = false
}

const selectCoOwnerShipSyndicate = () => {
  if (manualInputs.value) {
    closeCoOwnerShipSyndicateDialog()
    selected.value = [
      {
        registrationNumber: manualInfos.value.registrationNumber,
        usageName: manualInfos.value.usageName,
        address: {
          street: '',
          postalCode: '',
          city: '',
          country: 'FR',
        },
      },
    ]
  } else {
    closeCoOwnerShipSyndicateDialog()
    selected.value = selectedCoOwnerShipSyndicate.value
  }
}

const manualInputs = ref(true)
</script>
