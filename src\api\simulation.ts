import type { Page, Pageable } from '@/types/pagination'
import type { Operation, OperationRequest } from '@/types/operation'
import type { AxiosInstance, AxiosPromise, AxiosResponse } from 'axios'
import axiosInstance from '.'
import type { OperationFilter } from './operation'

class SimulationApi {
  public constructor(private axios: AxiosInstance) {}

  public create(simulation: OperationRequest): AxiosPromise<Operation> {
    return this.axios.post('/simulations', simulation)
  }

  public findAll(pageable: Pageable): AxiosPromise<Page<Operation>> {
    return this.axios.get('/simulations', {
      params: pageable,
    })
  }

  public findById(id: number): AxiosPromise<Operation> {
    return this.axios.get('/simulations/' + id).then((response: AxiosResponse<Operation>) => {
      response.data.coOwnerShipSyndicate = {
        address: {
          street: '',
          postalCode: '',
          city: '',
          country: 'FR',
        },
        registrationNumber: response.data.coOwnerShipSyndicateImmatriculationNumber ?? '',
        usageName: response.data.coOwnerShipSyndicateName ?? '',
      }
      return response
    })
  }

  public updateSimulation(id: number, simulationRequest: OperationRequest): AxiosPromise<Operation> {
    return this.axios.put('simulations/' + id, simulationRequest)
  }

  public delete(ids: number[]): AxiosPromise<void> {
    return this.axios.delete('simulations', { params: { ids } })
  }

  public cancelEmmyImport(id: number): AxiosPromise<Operation> {
    return this.axios.delete('operations/' + id + '/cancel_emmy_import')
  }

  public batchUpdate(
    filter: OperationFilter,
    request: Partial<Pick<OperationRequest, 'valuationTypeId' | 'instructorId'>>
  ): AxiosPromise<Operation> {
    return this.axios.put('operations/batch', request, {
      params: { ...filter },
    })
  }
}

export const simulationApi = new SimulationApi(axiosInstance)
